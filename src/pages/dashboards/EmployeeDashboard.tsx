import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch } from '@/store/store'
import { logout } from '@/features/auth/auth-slice'
import { 
  FaChartLine, 
  FaQuestionCircle, 
  FaVideo, 
  FaComments, 
  FaUser, 
  FaCertificate,
  FaBell,
  FaCalendarAlt,
  FaPlay,
  FaBookOpen,
  FaHeart,
  FaBrain,
  FaDumbbell,
  FaMedal,
  FaUsers,
  FaBullseye,
  FaHandshake,
  FaTrophy,
  FaFire,
  FaStar,
  FaCheckCircle,
  FaArrowUp,
  FaArrowDown,
  FaComment,
  FaThumbsUp,
  FaShare,
  FaGift,
  FaLightbulb,
  FaHeartbeat,
  FaClock,
  FaSmile,
  FaMeh,
  FaFrown,
  FaChevronRight,
  FaPlus,
  FaLeaf,
  FaBriefcase,
  FaDollarSign,
  FaRunning,
  FaGraduationCap,
  FaGlobe,
  FaCamera,
  FaQrcode,
  FaCog
} from 'react-icons/fa'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card'
import { Button } from '../../components/ui/Button'
import { Badge } from '../../components/ui/Badge'
import { LanguagePicker } from '../../components/ui/LanguagePicker'
import { AdminLayout } from '../../components/layout/AdminLayout'
import ProfilePictureManager from '../../components/employee/ProfilePictureManager'
import { EmployeeQRCode } from '../../components/employee/EmployeeQRCode'
import { useLanguage, SUPPORTED_LANGUAGES } from '../../utils/languageUtils'
import type { User } from '@/types/api'

interface EmployeeDashboardProps {
  user: User
}

// Comprehensive translation system
interface Translations {
  [key: string]: {
    EN: string
    AR: string
    HI: string
  }
}

const UI_TRANSLATIONS: Translations = {
  // Header and Navigation
  welcome: {
    EN: 'Welcome back',
    AR: 'مرحباً بعودتك',
    HI: 'वापसी पर स्वागत है'
  },
  questionsWaiting: {
    EN: 'questions waiting for your input',
    AR: 'أسئلة في انتظار إجاباتك',
    HI: 'प्रश्न आपके उत्तर की प्रतीक्षा में हैं'
  },
  overallScore: {
    EN: 'Overall Score',
    AR: 'النتيجة الإجمالية',
    HI: 'समग्र स्कोर'
  },
  refresh: {
    EN: 'Refresh',
    AR: 'تحديث',
    HI: 'रीफ्रेश'
  },
  language: {
    EN: 'Language',
    AR: 'اللغة',
    HI: 'भाषा'
  },
  
  // Tab Navigation
  wellnessOverview: {
    EN: 'Wellness Overview',
    AR: 'نظرة عامة على العافية',
    HI: 'कल्याण अवलोकन'
  },
  dailyCheckin: {
    EN: 'Daily Check-in',
    AR: 'التسجيل اليومي',
    HI: 'दैनिक चेक-इन'
  },
  wellnessDimensions: {
    EN: 'Wellness Dimensions',
    AR: 'أبعاد العافية',
    HI: 'कल्याण आयाम'
  },
  profileSettings: {
    EN: 'Profile Settings',
    AR: 'إعدادات الملف الشخصي',
    HI: 'प्रोफ़ाइल सेटिंग्स'
  },
  myQRCode: {
    EN: 'My QR Code',
    AR: 'رمز الاستجابة السريعة',
    HI: 'मेरा QR कोड'
  },

  mediaHub: {
    EN: 'Media Hub',
    AR: 'مركز الوسائط',
    HI: 'मीडिया हब'
  },
  consultantChat: {
    EN: 'Consultant Chat',
    AR: 'محادثة المستشار',
    HI: 'सलाहकार चैट'
  },
  myProgress: {
    EN: 'My Progress',
    AR: 'تقدمي',
    HI: 'मेरी प्रगति'
  },
  
  // Journey Progress
  journeyProgress: {
    EN: 'Your Wellness Journey Progress',
    AR: 'تقدم رحلة العافية الخاصة بك',
    HI: 'आपकी कल्याण यात्रा की प्रगति'
  },
  webinarIntro: {
    EN: 'Webinar Introduction',
    AR: 'مقدمة الندوة',
    HI: 'वेबिनार परिचय'
  },
  webinarBooking: {
    EN: 'Webinar Booking',
    AR: 'حجز الندوة',
    HI: 'वेबिनार बुकिंग'
  },
  preAssessment: {
    EN: 'Pre-Assessment',
    AR: 'التقييم المسبق',
    HI: 'पूर्व मूल्यांकन'
  },
  contentAccess: {
    EN: 'Content Access',
    AR: 'الوصول للمحتوى',
    HI: 'सामग्री पहुंच'
  },
  congratulations: {
    EN: '🎉 Congratulations! You now have full access to all wellness content and resources.',
    AR: '🎉 تهانينا! لديك الآن وصول كامل لجميع محتويات وموارد العافية.',
    HI: '🎉 बधाई हो! अब आपके पास सभी कल्याण सामग्री और संसाधनों तक पूर्ण पहुंच है।'
  },
  
  // Wellness Content
  dimensionsCount: {
    EN: 'dimensions',
    AR: 'أبعاد',
    HI: 'आयाम'
  },
  questions: {
    EN: 'questions',
    AR: 'أسئلة',
    HI: 'प्रश्न'
  },
  articles: {
    EN: 'articles',
    AR: 'مقالات',
    HI: 'लेख'
  },
  score: {
    EN: 'Score',
    AR: 'النتيجة',
    HI: 'स्कोर'
  },
  
  // Check-in
  todaysCheckin: {
    EN: "Today's Check-in",
    AR: 'تسجيل اليوم',
    HI: 'आज की चेक-इन'
  },
  scientificAssessment: {
    EN: 'Daily Wellness Check-in - Scientific Assessment',
    AR: 'التسجيل اليومي للعافية - تقييم علمي',
    HI: 'दैनिक कल्याण चेक-इन - वैज्ञानिक मूल्यांकन'
  },
  questionOf: {
    EN: 'Question',
    AR: 'السؤال',
    HI: 'प्रश्न'
  },
  of: {
    EN: 'of',
    AR: 'من',
    HI: 'का'
  },
  fivePointScale: {
    EN: 'Rate on a scale of 1-5 (1 = Strongly Disagree, 5 = Strongly Agree)',
    AR: 'قيم على مقياس من 1-5 (1 = أرفض بشدة، 5 = أوافق بشدة)',
    HI: '1-5 के पैमाने पर रेट करें (1 = बिल्कुल असहमत, 5 = पूर्णतः सहमत)'
  },
  responseRecorded: {
    EN: '✓ Response recorded',
    AR: '✓ تم تسجيل الإجابة',
    HI: '✓ उत्तर दर्ज किया गया'
  },
  completeCheckin: {
    EN: 'Complete Check-in',
    AR: 'إكمال التسجيل',
    HI: 'चेक-इन पूरा करें'
  },
  submitResponses: {
    EN: 'Submit Responses',
    AR: 'إرسال الإجابات',
    HI: 'उत्तर सबमिट करें'
  },
  
  // Wellness Content
  exploreContent: {
    EN: 'Explore Wellness Content',
    AR: 'استكشف محتوى العافية',
    HI: 'कल्याण सामग्री का अन्वेषण करें'
  },
  readMore: {
    EN: 'Read More',
    AR: 'اقرأ المزيد',
    HI: 'और पढ़ें'
  },
  minutes: {
    EN: 'min read',
    AR: 'دقيقة قراءة',
    HI: 'मिनट पढ़ें'
  },
  
  // Feedback
  howDoYouFeel: {
    EN: 'How do you feel today?',
    AR: 'كيف تشعر اليوم؟',
    HI: 'आज आप कैसा महसूस कर रहे हैं?'
  },
  great: {
    EN: 'Great',
    AR: 'رائع',
    HI: 'बहुत अच्छा'
  },
  okay: {
    EN: 'Okay',
    AR: 'جيد',
    HI: 'ठीक है'
  },
  notGood: {
    EN: 'Not Good',
    AR: 'ليس جيد',
    HI: 'अच्छा नहीं'
  },
  
  // Mobile-specific labels
  overview: {
    EN: 'Overview',
    AR: 'نظرة عامة',
    HI: 'अवलोकन'
  },
  checkin: {
    EN: 'Check-in',
    AR: 'تسجيل',
    HI: 'चेक-इन'
  },
  dimensions: {
    EN: 'Dimensions',
    AR: 'أبعاد',
    HI: 'आयाम'
  },
  profile: {
    EN: 'Profile',
    AR: 'الملف',
    HI: 'प्रोफ़ाइल'
  },
  qrcode: {
    EN: 'QR Code',
    AR: 'رمز QR',
    HI: 'QR कोड'
  }
}

// Multilingual content structure based on real QA Service data
interface WellnessContentItem {
  id: string
  titleEN: string
  titleAR: string
  titleHI: string
  descriptionEN: string
  descriptionAR: string
  descriptionHI: string
  category: string
  division: string
  readTime: number
  source: string
}

// Real content data from QA Service (Environmental & Intellectual & Emotional Wellbeing)
const WELLNESS_CONTENT: WellnessContentItem[] = [
  {
    id: 'EW01',
    titleEN: 'The place where you work affects the way you work: How does the environment shape your productivity?',
    titleAR: 'المكان الذي تعمل فيه يؤثر على الطريقة التي تعمل بها: كيف تشكّل البيئة إنتاجيتك؟',
    titleHI: 'जिस स्थान पर आप काम करते हैं, वह आपके काम करने के तरीके को प्रभावित करता है: आपकी उत्पादकता को पर्यावरण कैसे आकार देता है?',
    descriptionEN: 'An employee may be skilled, disciplined, and have high intentions, but if the work environment does not support them, their energy dissipates silently...',
    descriptionAR: 'قد يكون الموظف ماهرًا، منضبطًا، وذو نوايا عالية، لكن إن كانت بيئة العمل لا تدعمه، فإن طاقته تتبدد بصمت...',
    descriptionHI: 'कर्मचारी कुशल, अनुशासित और उच्च इरादों वाला हो सकता है, लेकिन अगर कार्यस्थल का वातावरण उसका समर्थन नहीं करता...',
    category: 'environmental',
    division: 'Environmental Wellbeing',
    readTime: 5,
    source: 'Harvard T.H. Chan School of Public Health'
  },
  {
    id: 'EW02',
    titleEN: 'Light, ventilation, colors: small details make a big difference in your performance.',
    titleAR: 'الضوء، التهوية، الألوان: تفاصيل صغيرة تُحدث فرقًا كبيرًا في أدائك',
    titleHI: 'प्रकाश, वेंटिलेशन, रंग: छोटी-छोटी बातें आपके प्रदर्शन में बड़ा अंतर ला सकती हैं।',
    descriptionEN: 'Research from the University of Oregon indicates that natural lighting reduces eye and mental stress and increases productivity by up to 40%...',
    descriptionAR: 'تشير أبحاث من University of Oregon إلى أن الإضاءة الطبيعية تقلل من الإجهاد العيني والعقلي، وتزيد من الإنتاجية بنسبة تصل إلى 40%...',
    descriptionHI: 'University of Oregon के शोध से पता चलता है कि प्राकृतिक प्रकाश आंखों और मानसिक तनाव को कम करता है, और उत्पादकता को 40% तक बढ़ा देता है...',
    category: 'environmental',
    division: 'Environmental Wellbeing',
    readTime: 4,
    source: 'University of Oregon'
  },
  {
    id: 'IW01',
    titleEN: 'Professional curiosity: How does it fuel thinking and drive performance?',
    titleAR: 'الفضول المهني: كيف يُغذي التفكير ويُحرّك الأداء؟',
    titleHI: 'पेशेवर जिज्ञासा: यह सोच को कैसे पोषित करती है और प्रदर्शन को कैसे प्रेरित करती है?',
    descriptionEN: 'Professional curiosity does not mean dissatisfaction, but rather a thirst for knowledge — not accepting things as they are without investigating them...',
    descriptionAR: 'الفضول المهني لا يعني عدم الرضا، بل يعني العطش المعرفي — أن لا تقبل الأمور كما هي دون أن تُفتّش خلفها...',
    descriptionHI: 'पेशेवर जिज्ञासा का मतलब असंतोष नहीं है, बल्कि ज्ञान की प्यास है — चीजों को वैसे ही स्वीकार न करना जैसा वे हैं...',
    category: 'intellectual',
    division: 'Intellectual Wellbeing',
    readTime: 6,
    source: 'Harvard Business School'
  },
  {
    id: 'EMW01',
    titleEN: 'How does your emotional state affect your professional day?',
    titleAR: 'كيف تؤثر حالتك العاطفية على يومك المهني؟',
    titleHI: 'आपकी भावनात्मक स्थिति आपके पेशेवर दिन को कैसे प्रभावित करती है?',
    descriptionEN: 'Research from the Yale Center for Emotional Intelligence indicates that employees who start their day in a negative mood achieve 20% less productivity...',
    descriptionAR: 'تشير أبحاث من Yale Center for Emotional Intelligence إلى أن الموظفين الذين يبدأون يومهم بمزاج سلبي يحققون إنتاجية أقل بنسبة 20%...',
    descriptionHI: 'Yale Center for Emotional Intelligence के शोध से पता चलता है कि जो कर्मचारी अपने दिन की शुरुआत नकारात्मक मूड के साथ करते हैं...',
    category: 'emotional',
    division: 'Emotional Wellbeing',
    readTime: 7,
    source: 'Yale Center for Emotional Intelligence'
  }
]

// 7 Wellness Divisions from the actual system
const WELLNESS_DIVISIONS = [
  {
    id: 'physical',
    name: 'Physical Wellbeing',
    description: 'Maintaining a healthy body through regular exercise, balanced nutrition, adequate sleep, and preventive healthcare measures.',
    icon: FaRunning,
    color: 'from-green-50 to-emerald-50',
    iconColor: 'text-emerald-500',
    score: 78,
    subdivisions: [
      { name: 'Energy & Nutrition', score: 82, questions: 2 },
      { name: 'Physical Activity & Endurance', score: 75, questions: 1 },
      { name: 'Rest & Recovery', score: 80, questions: 1 },
      { name: 'Preventive Health Literacy', score: 74, questions: 1 },
      { name: 'Musculoskeletal Mobility', score: 79, questions: 1 }
    ]
  },
  {
    id: 'emotional',
    name: 'Emotional Wellbeing',
    description: 'Understanding and managing one\'s emotions, coping effectively with stress, and maintaining a positive outlook.',
    icon: FaHeart,
    color: 'from-pink-50 to-rose-50',
    iconColor: 'text-rose-500',
    score: 85,
    subdivisions: [
      { name: 'Emotional Awareness & Regulation', score: 88, questions: 2 },
      { name: 'Positive Emotion & Restoration', score: 84, questions: 3 },
      { name: 'Adaptive Coping & Resilience', score: 82, questions: 2 },
      { name: 'Emotional Communication', score: 86, questions: 2 },
      { name: 'Stress Regulation Practices', score: 83, questions: 1 },
      { name: 'Social Support & Help-Seeking', score: 87, questions: 1 },
      { name: 'Emotional Boundaries & Balance', score: 85, questions: 1 },
      { name: 'Self-Compassion', score: 89, questions: 1 },
      { name: 'Cognitive Flexibility', score: 81, questions: 2 }
    ]
  },
  {
    id: 'social',
    name: 'Social Wellbeing',
    description: 'Developing a sense of connection, belonging, and a well-established support system.',
    icon: FaUsers,
    color: 'from-blue-50 to-indigo-50',
    iconColor: 'text-indigo-500',
    score: 79,
    subdivisions: [
      { name: 'Supportive Relationships', score: 82, questions: 2 },
      { name: 'Social Engagement & Joy', score: 77, questions: 2 },
      { name: 'Belonging & Inclusion', score: 78, questions: 1 },
      { name: 'Positive Communication', score: 81, questions: 1 }
    ]
  },
  {
    id: 'occupational',
    name: 'Occupational Wellbeing',
    description: 'Deriving personal satisfaction and enrichment from one\'s work while maintaining a healthy work-life balance.',
    icon: FaBriefcase,
    color: 'from-purple-50 to-violet-50',
    iconColor: 'text-violet-500',
    score: 73,
    subdivisions: [
      { name: 'Work Purpose & Meaning', score: 76, questions: 3 },
      { name: 'Work-Life Balance', score: 71, questions: 2 },
      { name: 'Supportive Work Environment', score: 74, questions: 2 },
      { name: 'Recognition & Value', score: 72, questions: 1 },
      { name: 'Professional Growth', score: 75, questions: 1 },
      { name: 'Autonomy & Ownership', score: 73, questions: 1 },
      { name: 'Workload Manageability', score: 70, questions: 1 },
      { name: 'Psychological Safety', score: 74, questions: 1 },
      { name: 'Feedback & Development Culture', score: 75, questions: 1 },
      { name: 'Time & Task Management', score: 72, questions: 1 },
      { name: 'Self-Efficacy at Work', score: 76, questions: 1 }
    ]
  },
  {
    id: 'intellectual',
    name: 'Intellectual Wellbeing',
    description: 'Engaging in creative and mentally stimulating activities to expand knowledge and skills.',
    icon: FaGraduationCap,
    color: 'from-yellow-50 to-amber-50',
    iconColor: 'text-amber-500',
    score: 81,
    subdivisions: [
      { name: 'Lifelong Learning Orientation', score: 84, questions: 2 },
      { name: 'Cognitive & Creative Stimulation', score: 79, questions: 1 },
      { name: 'Intellectual Fulfillment', score: 82, questions: 1 },
      { name: 'Curiosity & Openness', score: 80, questions: 1 },
      { name: 'Knowledge Integration & Sharing', score: 83, questions: 1 }
    ]
  },
  {
    id: 'environmental',
    name: 'Environmental Wellbeing',
    description: 'Recognizing the responsibility to preserve, protect, and improve the environment and appreciating one\'s surroundings.',
    icon: FaLeaf,
    color: 'from-teal-50 to-cyan-50',
    iconColor: 'text-teal-500',
    score: 76,
    subdivisions: [
      { name: 'Environmental Supportiveness', score: 78, questions: 2 },
      { name: 'Eco-Conscious Behavior', score: 74, questions: 1 },
      { name: 'Digital Environment Management', score: 77, questions: 1 },
      { name: 'Nature Connection & Restoration', score: 75, questions: 1 },
      { name: 'Space Organization & Comfort', score: 79, questions: 1 }
    ]
  },
  {
    id: 'financial',
    name: 'Financial Wellbeing',
    description: 'Managing financial resources effectively to live within one\'s means, set realistic goals, and prepare for future financial needs.',
    icon: FaDollarSign,
    color: 'from-orange-50 to-red-50',
    iconColor: 'text-orange-500',
    score: 68,
    subdivisions: [
      { name: 'Financial Control & Confidence', score: 70, questions: 3 },
      { name: 'Financial Planning & Decision-Making', score: 67, questions: 3 },
      { name: 'Financial Resilience', score: 69, questions: 1 },
      { name: 'Spending Discipline', score: 66, questions: 2 },
      { name: 'Debt Management', score: 68, questions: 1 },
      { name: 'Financial Security & Preparedness', score: 65, questions: 2 },
      { name: 'Financial Literacy & Advice-Seeking', score: 71, questions: 1 },
      { name: 'Financial Communication & Transparency', score: 69, questions: 1 },
      { name: 'Financial Motivation & Milestones', score: 67, questions: 1 },
      { name: 'Spending Balance & Financial Wellbeing', score: 70, questions: 2 }
    ]
  }
]

// Today's Questions from QA Service (scientific wellness questions)
const TODAYS_QUESTIONS = [
  {
    id: 'q1',
    division: 'Physical Wellbeing',
    subdivision: 'Energy & Nutrition',
    question: 'How energized did your meals make you feel today?',
    type: 'ongoing',
    options: [
      { text: 'Not at all energized', value: 100 },
      { text: 'Slightly energized', value: 101 },
      { text: 'Moderately energized', value: 102 },
      { text: 'Quite energized', value: 103 },
      { text: 'Very energized', value: 104 }
    ]
  },
  {
    id: 'q2',
    division: 'Emotional Wellbeing',
    subdivision: 'Emotional Awareness & Regulation',
    question: 'How emotionally balanced do you feel today?',
    type: 'ongoing',
    options: [
      { text: 'Very distressed', value: 200 },
      { text: 'Somewhat distressed', value: 201 },
      { text: 'Neutral', value: 202 },
      { text: 'Somewhat balanced', value: 203 },
      { text: 'Very balanced', value: 204 }
    ]
  },
  {
    id: 'q3',
    division: 'Occupational Wellbeing',
    subdivision: 'Work Purpose & Meaning',
    question: 'How motivated and engaged did you feel in your work today?',
    type: 'ongoing',
    options: [
      { text: 'Not at all motivated', value: 300 },
      { text: 'Slightly motivated', value: 301 },
      { text: 'Moderately motivated', value: 302 },
      { text: 'Quite motivated', value: 303 },
      { text: 'Highly motivated and engaged', value: 304 }
    ]
  }
]

// Assessment Flow Progress
const ASSESSMENT_FLOW = {
  currentStage: 'content_access',
  webinarIntroCompleted: true,
  webinarBookingCompleted: true,
  preAssessmentCompleted: true,
  webinarCompleted: false,
  postAssessmentCompleted: false,
  contentAccessGranted: true
}

const EmployeeDashboard = ({ user }: EmployeeDashboardProps) => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const [selectedTab, setSelectedTab] = useState<string>('overview')
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null)
  const [questionResponses, setQuestionResponses] = useState<{[key: string]: number}>({})

  
  // Enhanced language support with utilities
  const { 
    currentLanguage, 
    changeLanguage, 
    isRTL, 
    config: languageConfig, 
    rtlClasses,
    formatNumber,
    formatDate
  } = useLanguage()

  // Translation helper function
  const t = (key: string): string => {
    return UI_TRANSLATIONS[key]?.[currentLanguage] || UI_TRANSLATIONS[key]?.EN || key
  }

  // Get content based on current language
  const getLocalizedContent = (item: WellnessContentItem) => {
    return {
      title: currentLanguage === 'EN' ? item.titleEN : 
             currentLanguage === 'AR' ? item.titleAR : item.titleHI,
      description: currentLanguage === 'EN' ? item.descriptionEN : 
                   currentLanguage === 'AR' ? item.descriptionAR : item.descriptionHI
    }
  }

  // Filter content by division
  const filterContentByDivision = (divisionCategory: string) => {
    return WELLNESS_CONTENT.filter(item => item.category === divisionCategory)
  }

  const handleQuestionResponse = (questionId: string, value: number) => {
    setQuestionResponses(prev => ({
      ...prev,
      [questionId]: value
    }))
    // In real app, this would submit to QA service
    console.log(`Question ${questionId} answered with value: ${value}`)
  }



  const handleLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  const handleLanguageChange = React.useCallback((lang: 'EN' | 'AR' | 'HI') => {
    try {
      // Use requestAnimationFrame to prevent blocking the main thread
      requestAnimationFrame(() => {
        changeLanguage(lang)
        
        // Force component re-render after language change
        setTimeout(() => {
          // Trigger a state update to force re-render
          setSelectedTab(prev => prev)
        }, 100)
      })
    } catch (error) {
      console.error('Language change error:', error)
    }
  }, [changeLanguage])



  // Mobile-optimized tabs with shorter labels
  const tabs = [
    { 
      id: 'overview', 
      label: t('overview'), 
      fullLabel: t('wellnessOverview'),
      icon: FaChartLine 
    },
    { 
      id: 'checkin', 
      label: t('checkin'), 
      fullLabel: t('dailyCheckin'),
      icon: FaQuestionCircle 
    },
    { 
      id: 'divisions', 
      label: t('dimensions'), 
      fullLabel: t('wellnessDimensions'),
      icon: FaBullseye 
    },
    { 
      id: 'profile', 
      label: t('profile'), 
      fullLabel: t('profileSettings'),
      icon: FaCamera 
    },
    { 
      id: 'qrcode', 
      label: t('qrcode'), 
      fullLabel: t('myQRCode'),
      icon: FaQrcode 
    },
  ]

  return (
    <AdminLayout user={user} onLogout={handleLogout}>
      <div 
        key={`dashboard-${currentLanguage}`} 
        className={`max-w-7xl mx-auto px-3 py-4 sm:px-4 sm:py-6 lg:px-6 lg:py-8 ${rtlClasses.direction}`} 
        dir={isRTL ? 'rtl' : 'ltr'}
        style={{ direction: isRTL ? 'rtl' : 'ltr' }}
      >
        {/* Mobile-First Welcome Header */}
        <div className="mb-4 sm:mb-6 lg:mb-8">
          <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
            <div className={`${rtlClasses.textAlign} flex-1`}>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-sehatti-warm-gray-900 dark:text-white mb-1 sm:mb-2">
                {t('welcome')}, {user.name}! 🌟
              </h1>
              <p className="text-sm sm:text-base text-sehatti-warm-gray-600 dark:text-sehatti-warm-gray-300">
                {TODAYS_QUESTIONS.length} {t('questionsWaiting')}
              </p>
            </div>
            
            {/* Language Selector - Mobile Optimized */}
            <div className="flex-shrink-0">
              <LanguagePicker
                languages={SUPPORTED_LANGUAGES}
                currentLanguage={currentLanguage}
                onLanguageChange={handleLanguageChange}
                variant="compact"
              />
            </div>
          </div>
        </div>

        {/* Mobile-First Navigation Tabs */}
        <div className="border-b border-sehatti-warm-gray-200 mb-4 sm:mb-6 lg:mb-8">
          <div className={`flex overflow-x-auto scrollbar-hide pb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex gap-1 sm:gap-2 lg:gap-4 min-w-full ${isRTL ? 'flex-row-reverse' : ''}`}>
              {tabs.map((tab) => {
                const TabIcon = tab.icon
                const isActive = selectedTab === tab.id
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedTab(tab.id)}
                    className={`
                      flex flex-col sm:flex-row items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 sm:py-3 
                      border-b-2 transition-all duration-200 whitespace-nowrap min-w-[4rem] sm:min-w-[6rem]
                      touch-manipulation active:scale-95
                      ${isRTL ? 'sm:flex-row-reverse' : ''}
                      ${isActive 
                        ? 'border-sehatti-gold-500 text-sehatti-gold-600 bg-sehatti-gold-50/50' 
                        : 'border-transparent text-sehatti-warm-gray-600 hover:text-sehatti-warm-gray-900 hover:bg-sehatti-warm-gray-50'
                      }
                    `}
                  >
                    <TabIcon className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                    <span className="text-xs sm:text-sm lg:text-base font-medium leading-tight">
                      {/* Show short label on mobile, full label on desktop */}
                      <span className="sm:hidden">{tab.label}</span>
                      <span className="hidden sm:inline">{tab.fullLabel}</span>
                    </span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Mobile-First Tab Content */}
        {selectedTab === 'overview' && (
          <div className="space-y-4 sm:space-y-6">
            {/* Mobile-Optimized Assessment Flow Progress */}
            <Card className="touch-manipulation">
              <CardHeader className="pb-3 sm:pb-4">
                <CardTitle className={`flex items-center gap-2 text-base sm:text-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <FaCertificate className="w-4 h-4 sm:w-5 sm:h-5 text-sehatti-gold-500 flex-shrink-0" />
                  <span className="leading-tight">{t('journeyProgress')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                {/* Mobile: Stack vertically, Desktop: Horizontal */}
                <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 ${isRTL ? 'lg:grid-flow-col-dense' : ''}`}>
                  <div className={`flex items-center gap-3 p-3 rounded-lg bg-sehatti-warm-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 ${ASSESSMENT_FLOW.webinarIntroCompleted ? 'bg-green-500 text-white' : 'bg-gray-300'}`}>
                      {ASSESSMENT_FLOW.webinarIntroCompleted ? <FaCheckCircle className="w-4 h-4" /> : '1'}
                    </div>
                    <span className={`text-xs sm:text-sm font-medium leading-tight ${rtlClasses.textAlign}`}>{t('webinarIntro')}</span>
                  </div>
                    
                  <div className={`flex items-center gap-3 p-3 rounded-lg bg-sehatti-warm-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 ${ASSESSMENT_FLOW.webinarBookingCompleted ? 'bg-green-500 text-white' : 'bg-gray-300'}`}>
                      {ASSESSMENT_FLOW.webinarBookingCompleted ? <FaCheckCircle className="w-4 h-4" /> : '2'}
                    </div>
                    <span className={`text-xs sm:text-sm font-medium leading-tight ${rtlClasses.textAlign}`}>{t('webinarBooking')}</span>
                  </div>
                    
                  <div className={`flex items-center gap-3 p-3 rounded-lg bg-sehatti-warm-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 ${ASSESSMENT_FLOW.preAssessmentCompleted ? 'bg-green-500 text-white' : 'bg-gray-300'}`}>
                      {ASSESSMENT_FLOW.preAssessmentCompleted ? <FaCheckCircle className="w-4 h-4" /> : '3'}
                    </div>
                    <span className={`text-xs sm:text-sm font-medium leading-tight ${rtlClasses.textAlign}`}>{t('preAssessment')}</span>
                  </div>
                    
                  <div className={`flex items-center gap-3 p-3 rounded-lg bg-sehatti-warm-gray-50 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 ${ASSESSMENT_FLOW.contentAccessGranted ? 'bg-sehatti-gold-500 text-white' : 'bg-gray-300'}`}>
                      {ASSESSMENT_FLOW.contentAccessGranted ? <FaCheckCircle className="w-4 h-4" /> : '4'}
                    </div>
                    <span className={`text-xs sm:text-sm font-medium leading-tight ${rtlClasses.textAlign}`}>{t('contentAccess')}</span>
                  </div>
                </div>
                
                {ASSESSMENT_FLOW.contentAccessGranted && (
                  <div className={`p-3 sm:p-4 bg-green-50 rounded-lg ${rtlClasses.textAlign}`}>
                    <p className="text-green-800 font-medium text-sm sm:text-base leading-relaxed">{t('congratulations')}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Mobile-First Wellness Divisions Overview */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
              {WELLNESS_DIVISIONS.map((division) => {
                const DivisionIcon = division.icon
                const divisionContent = filterContentByDivision(division.id)
                
                return (
                  <Card key={division.id} className={`p-4 sm:p-5 lg:p-6 bg-gradient-to-r ${division.color} touch-manipulation active:scale-[0.98] transition-transform duration-200`}>
                    <div className={`flex items-center justify-between mb-3 sm:mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <DivisionIcon className={`w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 ${division.iconColor} flex-shrink-0`} />
                      <div className={`${rtlClasses.textAlign}`}>
                        <div className="text-xl sm:text-2xl font-bold text-gray-800 leading-tight">{division.score}</div>
                        <div className="text-xs sm:text-sm text-gray-600">{t('score')}</div>
                      </div>
                    </div>
                    <h3 className={`font-semibold text-gray-800 mb-2 text-sm sm:text-base leading-tight ${rtlClasses.textAlign}`}>{division.name}</h3>
                    <p className={`text-xs sm:text-sm text-gray-600 mb-3 leading-relaxed ${rtlClasses.textAlign}`}>{division.description}</p>
                    
                    {/* Mobile: Stack badges, Desktop: Horizontal */}
                    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 text-xs sm:text-sm ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 font-medium">{division.subdivisions.length} {t('dimensionsCount')}</span>
                      <div className={`flex gap-1 sm:gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Badge variant="outline" className="text-xs px-2 py-1">
                          {division.subdivisions.reduce((sum, sub) => sum + sub.questions, 0)} {t('questions')}
                        </Badge>
                        <Badge variant="secondary" className="text-xs px-2 py-1">
                          {divisionContent.length} {t('articles')}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>

            {/* Mobile-First Today's Questions Preview */}
            <Card className="touch-manipulation">
              <CardHeader className="pb-3 sm:pb-4">
                <CardTitle className={`flex items-center gap-2 text-base sm:text-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <FaQuestionCircle className="w-4 h-4 sm:w-5 sm:h-5 text-sehatti-gold-500 flex-shrink-0" />
                  <span className="leading-tight">
                    {t('todaysCheckin')} ({TODAYS_QUESTIONS.length} {t('questions')})
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3 sm:space-y-4">
                  {TODAYS_QUESTIONS.slice(0, 2).map((question) => (
                    <div key={question.id} className={`p-3 sm:p-4 border rounded-lg bg-sehatti-warm-gray-50/50 ${rtlClasses.textAlign}`}>
                      <div className={`flex flex-wrap items-center gap-1 sm:gap-2 mb-2 sm:mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Badge variant="outline" className="text-xs px-2 py-1">{question.division}</Badge>
                        <Badge variant="secondary" className="text-xs px-2 py-1">{question.subdivision}</Badge>
                      </div>
                      <p className="font-medium mb-2 text-sm sm:text-base leading-relaxed">{question.question}</p>
                      <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">{t('fivePointScale')}</p>
                    </div>
                  ))}
                  <Button 
                    onClick={() => setSelectedTab('checkin')} 
                    className="w-full min-h-[3rem] text-sm sm:text-base font-medium touch-manipulation active:scale-[0.98] transition-transform duration-200"
                    size="lg"
                  >
                    {t('completeCheckin')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'checkin' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <FaQuestionCircle className="w-5 h-5 text-sehatti-gold-500" />
                  {t('scientificAssessment')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-8">
                {TODAYS_QUESTIONS.map((question, index) => (
                  <div key={question.id} className="space-y-4">
                    <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Badge variant="outline">{question.division}</Badge>
                      <Badge variant="secondary">{question.subdivision}</Badge>
                      <span className={`text-sm text-gray-500 ${rtlClasses.textAlign}`}>{t('questionOf')} {index + 1} {t('of')} {TODAYS_QUESTIONS.length}</span>
                    </div>
                    
                    <h3 className={`text-lg font-medium mb-4 ${rtlClasses.textAlign}`}>{question.question}</h3>
                    
                    <div className="grid grid-cols-1 gap-3">
                      {question.options.map((option, optionIndex) => (
                      <button
                          key={option.value}
                          onClick={() => handleQuestionResponse(question.id, option.value)}
                          className={`p-4 border rounded-lg transition-all ${rtlClasses.textAlign} ${
                            questionResponses[question.id] === option.value
                            ? 'border-sehatti-gold-500 bg-sehatti-gold-50'
                            : 'border-gray-200 hover:border-sehatti-gold-300'
                        }`}
                      >
                          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>{option.text}</span>
                            <span className="text-sm text-gray-500">{optionIndex + 1}</span>
                          </div>
                      </button>
                    ))}
                  </div>
                    
                    {questionResponses[question.id] && (
                      <div className={`p-3 bg-green-50 rounded-lg ${rtlClasses.textAlign}`}>
                        <p className="text-green-800 text-sm">{t('responseRecorded')}</p>
                </div>
                    )}
                    
                    {index < TODAYS_QUESTIONS.length - 1 && <hr className="my-8" />}
                  </div>
                ))}
                
                {Object.keys(questionResponses).length === TODAYS_QUESTIONS.length && (
                  <div className="text-center">
                    <Button size="lg" className="px-8">
                      {t('submitResponses')}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

       

        {selectedTab === 'divisions' && (
          <div className="space-y-6">
            <h2 className={`text-2xl font-bold ${rtlClasses.textAlign}`}>{t('sevenDimensions')}</h2>
            
            <div className="space-y-6">
              {WELLNESS_DIVISIONS.map((division) => {
                const DivisionIcon = division.icon
                const divisionContent = filterContentByDivision(division.id)
                
                return (
                  <Card key={division.id}>
                    <CardHeader>
                      <CardTitle className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <DivisionIcon className={`w-6 h-6 ${division.iconColor}`} />
                        <span className={rtlClasses.textAlign}>{division.name}</span>
                        <Badge variant="outline">{division.score}/100</Badge>
                        <Badge variant="secondary">{divisionContent.length} {t('articles')}</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className={`text-gray-600 mb-4 ${rtlClasses.textAlign}`}>{division.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {division.subdivisions.map((sub) => (
                          <div key={sub.name} className={`p-3 border rounded-lg ${rtlClasses.textAlign}`}>
                            <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <h4 className="font-medium text-sm">{sub.name}</h4>
                              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <span className="text-sm font-medium">{sub.score}</span>
                                <Badge variant="secondary" className="text-xs">{sub.questions}Q</Badge>
                        </div>
                        </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-sehatti-gold-500 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${sub.score}%` }}
                              />
                        </div>
                          </div>
                        ))}
                      </div>
                      
                      {divisionContent.length > 0 && (
                        <div className={`mt-4 ${rtlClasses.textAlign}`}>
                          <h4 className="font-medium mb-2">{t('relatedContent')}</h4>
                          <div className={`flex flex-wrap gap-2 ${isRTL ? 'justify-end' : 'justify-start'}`}>
                            {divisionContent.map((content) => {
                              const localizedContent = getLocalizedContent(content)
                              return (
                                <Button
                                  key={content.id}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSelectedTab('content')}
                                  className="text-xs"
                                >
                                  {localizedContent.title.substring(0, 50)}...
                        </Button>
                              )
                            })}
                      </div>
                    </div>
                      )}
                  </CardContent>
                </Card>
                )
              })}
            </div>
          </div>
        )}



        {selectedTab === 'chat' && (
          <div className="space-y-6">
                            <Card className={`p-8 text-center ${rtlClasses.textAlign}`}>
            <CardContent>
                <FaComments className="w-16 h-16 text-sehatti-gold-500 mx-auto mb-4" />
                <h2 className="text-xl font-bold mb-4">{t('connectConsultants')}</h2>
                <p className="text-sehatti-warm-gray-600 mb-6">
                  {t('personalizedGuidance')}
                </p>
                <Button 
                  onClick={() => navigate('/employee/chat')}
                  size="lg"
                  className="px-8"
                >
                  {t('startConsultation')}
              </Button>
            </CardContent>
          </Card>
          </div>
        )}

        {selectedTab === 'profile' && (
          <div className="space-y-6">
            {/* Profile Picture Management */}
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <FaCamera className="w-5 h-5 text-sehatti-gold-500" />
                  {t('profileSettings')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ProfilePictureManager
                  currentProfilePicture={undefined}
                  userName={user.name}
                  userId={user.id}
                  onProfilePictureUpdate={(newUrl) => {
                    // In real implementation, update user state
                    console.log('Profile picture updated:', newUrl)
                  }}
                  size="lg"
                />
              </CardContent>
            </Card>

            {/* Language Settings */}
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <FaGlobe className="w-5 h-5 text-sehatti-gold-500" />
                  Language Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {SUPPORTED_LANGUAGES.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => changeLanguage(lang.code)}
                      className={`p-4 border-2 rounded-lg transition-all ${
                        currentLanguage === lang.code
                          ? 'border-sehatti-gold-500 bg-sehatti-gold-50'
                          : 'border-gray-200 hover:border-sehatti-gold-300'
                      }`}
                    >
                      <div className="text-center">
                        <div className="text-2xl mb-2">{lang.flag}</div>
                        <div className="font-medium">{lang.name}</div>
                        <div className="text-sm text-gray-600">{lang.nativeName}</div>
                        {lang.isRTL && (
                          <Badge variant="outline" className="mt-2 text-xs">RTL</Badge>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
                <div className="mt-4 text-sm text-gray-600">
                  <p>Language preference is automatically saved and applied across all sessions.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedTab === 'qrcode' && (
          <div className="space-y-6">
            <div className="max-w-2xl mx-auto">
              <EmployeeQRCode
                user={{
                  id: user.id,
                  name: user.name,
                  email: user.email,
                                     company_id: user.companyId || 'sehatti-default',
                  department: user.department || undefined,
                  position: user.role || undefined,
                  employee_id: user.id
                }}
                size="lg"
              />
            </div>
          </div>
        )}
      </div>


    </AdminLayout>
  )
}

export default EmployeeDashboard 