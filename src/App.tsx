import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { Toaster } from 'react-hot-toast'

import { ThemeProvider } from './components/ThemeProvider'
import { AuthProvider } from './components/AuthProvider'
import { store } from './store/store'

// Import pages
import Login from './pages/Login'
import Register from './pages/Register'
import ForgotPassword from './pages/ForgotPassword'
import ResetPassword from './pages/ResetPassword'
import SetPassword from './pages/SetPassword'
import RoleDashboard from './pages/RoleDashboard'
import QAManagement from './pages/QAManagement'
import CorporatesManagement from './pages/CorporatesManagement'
import CorporateSettings from './pages/CorporateSettings'
import MediaManagement from './pages/MediaManagement'

import ConsultantManagement from './pages/ConsultantManagement'
import InvitationManagement from './pages/InvitationManagement'
import HRAdminDashboard from './pages/HRAdminDashboard'
import HRAnalyticsDashboard from './pages/HRAnalyticsDashboard'
import HREmployeeManagement from './pages/HREmployeeManagement'
import HRNotificationManagement from './pages/HRNotificationManagement'
import HRInvitationManagement from './pages/HRInvitationManagement'
import AdminDashboardWrapper from './pages/AdminDashboardWrapper'
import EmployeeOnboarding from './pages/employee/EmployeeOnboarding'
import EmployeeMediaHub from './pages/employee/EmployeeMediaHub'
import EmployeeDailyCheckin from './pages/employee/EmployeeDailyCheckin'
import EmployeeProfile from './pages/employee/EmployeeProfile'
import EmployeeChat from './pages/employee/EmployeeChat'
import EmployeeDashboardWrapper from './pages/employee/EmployeeDashboardWrapper'
import ArticleReaderWrapper from './pages/employee/ArticleReaderWrapper'
import ConsultantChat from './pages/consultant/ConsultantChat'
import ConsultantDashboard from './pages/consultant/ConsultantDashboard'

// Import guards
import ProtectedRoute from './guard/ProtectedRoute'
import RoleBasedRoute from './guard/RoleBasedRoute'

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider defaultTheme="light" storageKey="sehatti-theme">
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/set-password" element={<SetPassword />} />
              <Route 
                path="/dashboard" 
                element={
                  <ProtectedRoute>
                    <RoleDashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/dashboard" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <AdminDashboardWrapper />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/qa-management" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <QAManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
            
              <Route 
                path="/admin/corporates" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <CorporatesManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/corporate/:id/settings" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <CorporateSettings />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/corporate-settings/:companyId" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <CorporateSettings />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/company-settings" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <CorporateSettings />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/media-management" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <MediaManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />

              <Route 
                path="/admin/consultants" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <ConsultantManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin/invitation-management" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['SYSTEM_ADMIN']}>
                      <InvitationManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              {/* HR Admin Routes */}
              <Route 
                path="/hr/dashboard" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <HRAdminDashboard />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/employees" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <HREmployeeManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/notifications" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <HRNotificationManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/invitations" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <HRInvitationManagement />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/analytics" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <HRAnalyticsDashboard />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/surveys" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <div className="p-8 text-center">
                        <h1 className="text-2xl font-bold mb-4">Survey Management</h1>
                        <p>Survey management coming soon...</p>
                      </div>
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/hr/settings" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['HR_ADMIN']}>
                      <div className="p-8 text-center">
                        <h1 className="text-2xl font-bold mb-4">HR Settings</h1>
                        <p>Settings management coming soon...</p>
                      </div>
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              {/* HR Base Route Redirect */}
              <Route path="/hr" element={<Navigate to="/hr/dashboard" replace />} />
              
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              
              {/* Employee Routes */}
              <Route 
                path="/employee/onboarding" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeOnboarding />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              {/* Employee Dashboard Route */}
              <Route 
                path="/employee/dashboard" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeDashboardWrapper />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />

              {/* Employee other pages */}
              <Route 
                path="/employee/analytics" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <div className="p-8 text-center">
                        <h1 className="text-2xl font-bold mb-4">Analytics</h1>
                        <p>Employee analytics coming soon...</p>
                      </div>
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/employee/checkin" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeDailyCheckin />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/employee/media" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeMediaHub />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/employee/chat" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeChat />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/employee/profile" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <EmployeeProfile />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/employee/article/:articleId" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['EMPLOYEE']}>
                      <ArticleReaderWrapper />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />

              {/* Consultant Routes */}
              <Route 
                path="/consultant/dashboard" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['CONSULTANT']}>
                      <ConsultantDashboard />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/consultant/chat" 
                element={
                  <ProtectedRoute>
                    <RoleBasedRoute allowedRoles={['CONSULTANT']}>
                      <ConsultantChat />
                    </RoleBasedRoute>
                  </ProtectedRoute>
                } 
              />
              
              {/* Consultant Base Route Redirect */}
              <Route path="/consultant" element={<Navigate to="/consultant/dashboard" replace />} />
            </Routes>
          </Router>
          
          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              style: {
                background: '#fff',
                color: '#374151',
                border: '1px solid #e5e7eb'
              }
            }}
          />
        </AuthProvider>
      </ThemeProvider>
    </Provider>
  )
}

export default App
