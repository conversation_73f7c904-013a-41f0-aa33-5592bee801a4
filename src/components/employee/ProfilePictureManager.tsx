import React, { useState, useRef, useCallback } from 'react'
import { 
  FaCamera, 
  FaUpload, 
  FaTrash, 
  FaEdit, 
  FaUser,
  FaCheckCircle,
  FaTimes,
  FaSpinner
} from 'react-icons/fa'
import { Button } from '../ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card'
import { Badge } from '../ui/Badge'
import { Alert } from '../ui/Alert'
import toast from 'react-hot-toast'

interface ProfilePictureManagerProps {
  currentProfilePicture?: string
  userName: string
  userId: string
  onProfilePictureUpdate?: (newPictureUrl: string) => void
  maxFileSize?: number // in MB
  allowedFormats?: string[]
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

const ProfilePictureManager: React.FC<ProfilePictureManagerProps> = ({
  currentProfilePicture,
  userName,
  userId,
  onProfilePictureUpdate,
  maxFileSize = 5,
  allowedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  size = 'lg'
}) => {
  const [isUploading, setIsUploading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [showCropModal, setShowCropModal] = useState(false)
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 200, height: 200 })
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Size configurations
  const sizeConfig = {
    sm: { size: 'w-16 h-16', textSize: 'text-xs', iconSize: 'w-4 h-4' },
    md: { size: 'w-24 h-24', textSize: 'text-sm', iconSize: 'w-5 h-5' },
    lg: { size: 'w-32 h-32', textSize: 'text-base', iconSize: 'w-6 h-6' },
    xl: { size: 'w-40 h-40', textSize: 'text-lg', iconSize: 'w-8 h-8' }
  }

  const config = sizeConfig[size]

  // Generate initials for fallback avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Validate file
  const validateFile = (file: File): string | null => {
    if (!allowedFormats.includes(file.type)) {
      return `Invalid file format. Allowed formats: ${allowedFormats.join(', ')}`
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size too large. Maximum size: ${maxFileSize}MB`
    }
    
    return null
  }

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    const error = validateFile(file)
    if (error) {
      toast.error(error)
      return
    }

    setSelectedFile(file)
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
    setShowCropModal(true)
  }, [allowedFormats, maxFileSize])

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const file = e.dataTransfer.files[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  // Upload profile picture
  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    try {
      // Create FormData for file upload
      const formData = new FormData()
      formData.append('profilePicture', selectedFile)
      formData.append('userId', userId)
      formData.append('cropArea', JSON.stringify(cropArea))

      // In real implementation, this would call the API
      // const response = await fetch('/api/v2/users/profile-picture', {
      //   method: 'POST',
      //   body: formData,
      //   headers: {
      //     'Authorization': `Bearer ${getAuthToken()}`
      //   }
      // })
      
      // Mock API response for now
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockNewPictureUrl = previewUrl || URL.createObjectURL(selectedFile)
      
      toast.success('Profile picture updated successfully!')
      onProfilePictureUpdate?.(mockNewPictureUrl)
      
      // Reset state
      setSelectedFile(null)
      setPreviewUrl(null)
      setShowCropModal(false)
      
    } catch (error) {
      console.error('Error uploading profile picture:', error)
      toast.error('Failed to upload profile picture. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  // Remove profile picture
  const handleRemove = async () => {
    try {
      // In real implementation, this would call the API
      // await fetch(`/api/v2/users/${userId}/profile-picture`, {
      //   method: 'DELETE',
      //   headers: {
      //     'Authorization': `Bearer ${getAuthToken()}`
      //   }
      // })
      
      toast.success('Profile picture removed successfully!')
      onProfilePictureUpdate?.('')
      
    } catch (error) {
      console.error('Error removing profile picture:', error)
      toast.error('Failed to remove profile picture. Please try again.')
    }
  }

  // Trigger file input
  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-4">
      {/* Profile Picture Display */}
      <div className="flex items-center justify-center">
        <div className="relative group">
          <div 
            className={`${config.size} rounded-full border-4 border-sehatti-gold-200 overflow-hidden bg-gradient-to-br from-sehatti-gold-100 to-sehatti-gold-200 flex items-center justify-center relative`}
          >
            {currentProfilePicture ? (
              <img 
                src={currentProfilePicture} 
                alt={`${userName}'s profile`}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className={`${config.textSize} font-bold text-sehatti-gold-700`}>
                {getInitials(userName)}
              </div>
            )}
            
            {/* Upload overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <FaCamera className={`${config.iconSize} text-white`} />
              </div>
            </div>
          </div>
          
          {/* Edit button */}
          <button
            onClick={triggerFileInput}
            className="absolute -bottom-1 -right-1 bg-sehatti-gold-500 hover:bg-sehatti-gold-600 text-white rounded-full p-2 shadow-lg transition-colors duration-200"
          >
            <FaEdit className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-2 justify-center">
        <Button
          onClick={triggerFileInput}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <FaUpload className="w-4 h-4" />
          Upload New Photo
        </Button>
        
        {currentProfilePicture && (
          <Button
            onClick={handleRemove}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
          >
            <FaTrash className="w-4 h-4" />
            Remove Photo
          </Button>
        )}
      </div>

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={allowedFormats.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Drag and Drop Area */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200
          ${isDragging 
            ? 'border-sehatti-gold-400 bg-sehatti-gold-50' 
            : 'border-gray-300 hover:border-sehatti-gold-300 hover:bg-gray-50'
          }
        `}
      >
        <FaUpload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600 mb-1">
          Drag and drop your photo here, or click to browse
        </p>
        <p className="text-xs text-gray-500">
          Supported formats: JPG, PNG, WebP • Max size: {maxFileSize}MB
        </p>
      </div>

      {/* Upload Requirements */}
      <div className="text-xs text-gray-500 space-y-1">
        <div className="flex items-center gap-2">
          <FaCheckCircle className="w-3 h-3 text-green-500" />
          <span>Square photos work best (1:1 ratio)</span>
        </div>
        <div className="flex items-center gap-2">
          <FaCheckCircle className="w-3 h-3 text-green-500" />
          <span>Minimum resolution: 200x200 pixels</span>
        </div>
        <div className="flex items-center gap-2">
          <FaCheckCircle className="w-3 h-3 text-green-500" />
          <span>Clear, well-lit photos are recommended</span>
        </div>
      </div>

      {/* Crop Modal */}
      {showCropModal && previewUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Crop Your Photo</span>
                <button
                  onClick={() => setShowCropModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FaTimes className="w-4 h-4" />
                </button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Image Preview */}
                <div className="flex justify-center">
                  <div className="relative w-64 h-64 border-2 border-gray-300 rounded-lg overflow-hidden">
                    <img 
                      src={previewUrl} 
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                    {/* Crop overlay would go here in a real implementation */}
                  </div>
                </div>
                
                <Alert>
                  <span className="text-sm">
                    Image will be automatically cropped to fit a circular profile picture.
                  </span>
                </Alert>
                
                {/* Action buttons */}
                <div className="flex gap-2 justify-end">
                  <Button
                    onClick={() => setShowCropModal(false)}
                    variant="outline"
                    size="sm"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleUpload}
                    size="sm"
                    isLoading={isUploading}
                    className="flex items-center gap-2"
                  >
                    {isUploading ? (
                      <>
                        <FaSpinner className="w-4 h-4 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <FaCheckCircle className="w-4 h-4" />
                        Save Photo
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

export default ProfilePictureManager 